<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestionnaire de Groupes - DJEMIAL MOUSTAPHA</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4472C4, #5B9BD5);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .logo-corner {
            position: absolute;
            top: 20px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd700);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 10px;
            text-align: center;
            border: 3px solid white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .toolbar {
            padding: 25px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-primary { background: linear-gradient(135deg, #28a745, #20c997); }
        .btn-secondary { background: linear-gradient(135deg, #6c757d, #495057); }
        .btn-danger { background: linear-gradient(135deg, #dc3545, #c82333); }
        .btn-info { background: linear-gradient(135deg, #17a2b8, #138496); }
        .stats {
            padding: 20px 30px;
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 20px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
            flex: 1;
            min-width: 200px;
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #4472C4;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }
        .groups-grid {
            padding: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }
        .group-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        .group-card:hover {
            border-color: #4472C4;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .group-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #4472C4, #5B9BD5);
        }
        .group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .group-name {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
        }
        .group-number {
            background: linear-gradient(135deg, #4472C4, #5B9BD5);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .group-info {
            margin-bottom: 20px;
        }
        .group-info div {
            margin-bottom: 8px;
            color: #6c757d;
            font-size: 14px;
        }
        .group-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        .btn-small {
            padding: 8px 15px;
            font-size: 12px;
            border-radius: 6px;
        }
        .add-group-card {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border: 2px dashed #28a745;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #28a745;
            font-size: 18px;
            font-weight: bold;
            min-height: 200px;
        }
        .add-group-card:hover {
            background: linear-gradient(135deg, #d4edda, #e8f5e8);
            border-color: #20c997;
            color: #20c997;
        }
        .add-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }
        @keyframes modalSlideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }
        .modal-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        .close:hover {
            color: #dc3545;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #4472C4;
            box-shadow: 0 0 0 3px rgba(68, 114, 196, 0.1);
        }
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }
        @media (max-width: 768px) {
            .container { margin: 10px; border-radius: 10px; }
            .header { padding: 20px; }
            .header h1 { font-size: 22px; }
            .toolbar { padding: 15px 20px; flex-direction: column; align-items: stretch; }
            .groups-grid { grid-template-columns: 1fr; padding: 20px; }
            .stats { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-corner">Facilite<br>Chaef</div>
            <h1>GESTIONNAIRE DE GROUPES</h1>
            <p>Système de gestion des groupes de commandes clients</p>
        </div>

        <div class="toolbar">
            <button class="btn btn-primary" onclick="ouvrirModalNouveauGroupe()">➕ Nouveau Groupe</button>
            <button class="btn btn-secondary" onclick="actualiserGroupes()">🔄 Actualiser</button>
            <button class="btn btn-info" onclick="exporterGroupes()">📊 Exporter Données</button>
            <button class="btn btn-danger" onclick="supprimerGroupesSelectionnes()">🗑️ Supprimer Sélectionnés</button>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalGroupes">0</div>
                <div class="stat-label">Groupes Totaux</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalClients">0</div>
                <div class="stat-label">Clients Totaux</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="chiffreAffaires">0 DA</div>
                <div class="stat-label">Chiffre d'Affaires</div>
            </div>
        </div>

        <div class="groups-grid" id="groupsGrid"></div>
    </div>

    <div id="modalNouveauGroupe" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Créer Nouveau Groupe</h2>
                <span class="close" onclick="fermerModal()">&times;</span>
            </div>
            <form id="formNouveauGroupe">
                <div class="form-group">
                    <label class="form-label" for="nomGroupe">Nom du Groupe *</label>
                    <input type="text" id="nomGroupe" class="form-control" placeholder="Ex: GROUPE 03" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="descriptionGroupe">Description</label>
                    <textarea id="descriptionGroupe" class="form-control" rows="3" placeholder="Description du groupe (optionnel)"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label" for="dateCreation">Date de Création</label>
                    <input type="date" id="dateCreation" class="form-control">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="fermerModal()">Annuler</button>
                    <button type="submit" class="btn btn-primary">Créer Groupe</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let groupes = [];

        document.addEventListener('DOMContentLoaded', function() {
            chargerGroupes();
            mettreAJourStatistiques();
            document.getElementById('dateCreation').value = new Date().toISOString().split('T')[0];
        });

        function chargerGroupes() {
            const groupesSauvegardes = localStorage.getItem('groupes');
            if (groupesSauvegardes) {
                groupes = JSON.parse(groupesSauvegardes);
            } else {
                groupes = [{
                    id: 'groupe-02',
                    nom: 'GROUPE 02',
                    description: 'Groupe principal de commandes clients',
                    dateCreation: '2024-10-21',
                    nombreClients: 0,
                    chiffreAffaires: 0,
                    statut: 'actif'
                }];
                sauvegarderGroupes();
            }
            afficherGroupes();
        }

        function sauvegarderGroupes() {
            localStorage.setItem('groupes', JSON.stringify(groupes));
        }

        function afficherGroupes() {
            const grid = document.getElementById('groupsGrid');
            grid.innerHTML = '';

            const addCard = document.createElement('div');
            addCard.className = 'group-card add-group-card';
            addCard.onclick = ouvrirModalNouveauGroupe;
            addCard.innerHTML = '<div class="add-icon">➕</div><div>Créer Nouveau Groupe</div>';
            grid.appendChild(addCard);

            groupes.forEach(groupe => {
                const card = document.createElement('div');
                card.className = 'group-card';
                card.onclick = () => ouvrirGroupe(groupe.id);

                card.innerHTML =
                    '<div class="group-header">' +
                        '<div class="group-name">' + groupe.nom + '</div>' +
                        '<div class="group-number">' + groupe.id.toUpperCase() + '</div>' +
                    '</div>' +
                    '<div class="group-info">' +
                        '<div><strong>📅 Créé le:</strong> ' + formatDate(groupe.dateCreation) + '</div>' +
                        '<div><strong>👥 Clients:</strong> ' + (groupe.nombreClients || 0) + '</div>' +
                        '<div><strong>💰 CA:</strong> ' + formatMontant(groupe.chiffreAffaires || 0) + ' DA</div>' +
                        '<div><strong>📝 Description:</strong> ' + (groupe.description || 'Aucune description') + '</div>' +
                    '</div>' +
                    '<div class="group-actions" onclick="event.stopPropagation()">' +
                        '<button class="btn btn-primary btn-small" onclick="creerPageGroupe(\'' + groupe.id + '\')">📄 Ouvrir Page</button>' +
                        '<button class="btn btn-info btn-small" onclick="modifierGroupe(\'' + groupe.id + '\')">✏️ Modifier</button>' +
                        '<button class="btn btn-danger btn-small" onclick="supprimerGroupe(\'' + groupe.id + '\')">🗑️ Supprimer</button>' +
                    '</div>';

                grid.appendChild(card);
            });
        }

        function mettreAJourStatistiques() {
            const totalGroupes = groupes.length;
            let totalClients = 0;
            let chiffreAffaires = 0;

            groupes.forEach(groupe => {
                totalClients += groupe.nombreClients || 0;
                chiffreAffaires += groupe.chiffreAffaires || 0;
            });

            document.getElementById('totalGroupes').textContent = totalGroupes;
            document.getElementById('totalClients').textContent = totalClients;
            document.getElementById('chiffreAffaires').textContent = formatMontant(chiffreAffaires) + ' DA';
        }

        function ouvrirModalNouveauGroupe() {
            document.getElementById('modalNouveauGroupe').style.display = 'block';
            document.getElementById('nomGroupe').focus();
        }

        function fermerModal() {
            document.getElementById('modalNouveauGroupe').style.display = 'none';
            document.getElementById('formNouveauGroupe').reset();
            document.getElementById('dateCreation').value = new Date().toISOString().split('T')[0];
        }

        document.getElementById('formNouveauGroupe').addEventListener('submit', function(e) {
            e.preventDefault();

            const nom = document.getElementById('nomGroupe').value.trim();
            const description = document.getElementById('descriptionGroupe').value.trim();
            const dateCreation = document.getElementById('dateCreation').value;

            if (!nom) {
                alert('Le nom du groupe est obligatoire!');
                return;
            }

            if (groupes.some(g => g.nom.toLowerCase() === nom.toLowerCase())) {
                alert('Un groupe avec ce nom existe déjà!');
                return;
            }

            const id = 'groupe-' + nom.toLowerCase().replace(/[^a-z0-9]/g, '-');

            const nouveauGroupe = {
                id: id,
                nom: nom,
                description: description,
                dateCreation: dateCreation,
                nombreClients: 0,
                chiffreAffaires: 0,
                statut: 'actif'
            };

            groupes.push(nouveauGroupe);
            sauvegarderGroupes();
            afficherGroupes();
            mettreAJourStatistiques();
            fermerModal();

            alert('Groupe "' + nom + '" créé avec succès!');
        });

        function ouvrirGroupe(groupeId) {
            creerPageGroupe(groupeId);
        }

        function creerPageGroupe(groupeId) {
            const groupe = groupes.find(g => g.id === groupeId);
            if (groupe) {
                if (groupe.id === 'groupe-02') {
                    window.location.href = 'GestionnaireCommandes.html';
                } else {
                    const nomFichier = 'Gestionnaire_' + groupe.nom.replace(/[^a-zA-Z0-9]/g, '_') + '.html';
                    const contenuHTML = genererHTMLGroupe(groupe);

                    const blob = new Blob([contenuHTML], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = nomFichier;
                    link.click();
                    URL.revokeObjectURL(url);

                    alert('✅ Page créée: ' + nomFichier + '\n\n📄 Sauvegardez ce fichier dans votre dossier\n🔧 Cette page est vide et prête pour vos données!');
                }
            }
        }

        function modifierGroupe(groupeId) {
            const groupe = groupes.find(g => g.id === groupeId);
            if (groupe) {
                const nouveauNom = prompt('Nouveau nom du groupe:', groupe.nom);
                if (nouveauNom && nouveauNom.trim()) {
                    if (groupes.some(g => g.id !== groupeId && g.nom.toLowerCase() === nouveauNom.toLowerCase())) {
                        alert('Un groupe avec ce nom existe déjà!');
                        return;
                    }

                    groupe.nom = nouveauNom.trim();

                    const nouvelleDescription = prompt('Nouvelle description:', groupe.description || '');
                    if (nouvelleDescription !== null) {
                        groupe.description = nouvelleDescription.trim();
                    }

                    sauvegarderGroupes();
                    afficherGroupes();
                    alert('Groupe modifié avec succès!');
                }
            }
        }

        function supprimerGroupe(groupeId) {
            event.stopPropagation();

            const groupe = groupes.find(g => g.id === groupeId);
            if (groupe) {
                if (groupe.id === 'groupe-02') {
                    if (!confirm('⚠️ ATTENTION: Vous êtes sur le point de supprimer GROUPE 02 qui contient vos données principales!\n\nÊtes-vous absolument sûr de vouloir continuer?\n\nCette action supprimera:\n• Le groupe GROUPE 02\n• Toutes ses données de commandes\n• Tous les clients associés\n\nCette action est IRRÉVERSIBLE!')) {
                        return;
                    }
                }

                const message = 'Êtes-vous sûr de vouloir supprimer le groupe "' + groupe.nom + '"?\n\nCette action supprimera:\n• Le groupe et ses informations\n• Toutes les données de commandes associées\n• Tous les clients de ce groupe\n\nCette action est irréversible!';

                if (confirm(message)) {
                    localStorage.removeItem('commandesData_' + groupeId);
                    groupes = groupes.filter(g => g.id !== groupeId);
                    sauvegarderGroupes();
                    afficherGroupes();
                    mettreAJourStatistiques();
                    alert('✅ Groupe "' + groupe.nom + '" supprimé avec succès!\n\n🗑️ Toutes les données associées ont été effacées.');
                }
            }
        }

        function actualiserGroupes() {
            groupes.forEach(groupe => {
                const commandesData = localStorage.getItem('commandesData_' + groupe.id);
                if (commandesData) {
                    const commandes = JSON.parse(commandesData);
                    groupe.nombreClients = commandes.length;
                    groupe.chiffreAffaires = commandes.reduce((total, cmd) => total + (cmd.prix || 0), 0);
                } else {
                    groupe.nombreClients = 0;
                    groupe.chiffreAffaires = 0;
                }
            });

            sauvegarderGroupes();
            afficherGroupes();
            mettreAJourStatistiques();
            alert('Données actualisées!');
        }

        function exporterGroupes() {
            const dataStr = JSON.stringify(groupes, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'groupes_export_' + new Date().toISOString().split('T')[0] + '.json';
            link.click();
        }

        function supprimerGroupesSelectionnes() {
            alert('Fonctionnalité en développement: Sélection multiple de groupes');
        }

        function genererHTMLGroupe(groupe) {
            return '<!DOCTYPE html>\n' +
'<html lang="fr">\n' +
'<head>\n' +
'    <meta charset="UTF-8">\n' +
'    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n' +
'    <title>Gestionnaire ' + groupe.nom + ' - DJEMIAL MOUSTAPHA</title>\n' +
'    <style>\n' +
'        * { margin: 0; padding: 0; box-sizing: border-box; }\n' +
'        body {\n' +
'            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;\n' +
'            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n' +
'            min-height: 100vh;\n' +
'            padding: 20px;\n' +
'        }\n' +
'        .container {\n' +
'            max-width: 1400px;\n' +
'            margin: 0 auto;\n' +
'            background: white;\n' +
'            border-radius: 15px;\n' +
'            box-shadow: 0 20px 40px rgba(0,0,0,0.1);\n' +
'            overflow: hidden;\n' +
'        }\n' +
'        .header {\n' +
'            background: linear-gradient(135deg, #4472C4, #5B9BD5);\n' +
'            color: white;\n' +
'            padding: 30px;\n' +
'            text-align: center;\n' +
'            position: relative;\n' +
'        }\n' +
'        .header h1 {\n' +
'            font-size: 28px;\n' +
'            margin-bottom: 10px;\n' +
'            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n' +
'        }\n' +
'        .header p {\n' +
'            font-size: 16px;\n' +
'            opacity: 0.9;\n' +
'        }\n' +
'        .logo-corner {\n' +
'            position: absolute;\n' +
'            top: 20px;\n' +
'            right: 30px;\n' +
'            width: 60px;\n' +
'            height: 60px;\n' +
'            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd700);\n' +
'            border-radius: 50%;\n' +
'            display: flex;\n' +
'            align-items: center;\n' +
'            justify-content: center;\n' +
'            color: white;\n' +
'            font-weight: bold;\n' +
'            font-size: 10px;\n' +
'            text-align: center;\n' +
'            border: 3px solid white;\n' +
'            box-shadow: 0 4px 8px rgba(0,0,0,0.2);\n' +
'        }\n' +
'        .group-info-bar {\n' +
'            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);\n' +
'            padding: 20px 30px;\n' +
'            display: flex;\n' +
'            justify-content: space-between;\n' +
'            align-items: center;\n' +
'            flex-wrap: wrap;\n' +
'            gap: 20px;\n' +
'        }\n' +
'        .group-details {\n' +
'            display: flex;\n' +
'            gap: 30px;\n' +
'            flex-wrap: wrap;\n' +
'        }\n' +
'        .group-detail {\n' +
'            text-align: center;\n' +
'        }\n' +
'        .group-detail-label {\n' +
'            font-size: 12px;\n' +
'            color: #6c757d;\n' +
'            margin-bottom: 5px;\n' +
'        }\n' +
'        .group-detail-value {\n' +
'            font-size: 18px;\n' +
'            font-weight: bold;\n' +
'            color: #2c3e50;\n' +
'        }\n' +
'        .back-button {\n' +
'            background: linear-gradient(135deg, #6c757d, #495057);\n' +
'            color: white;\n' +
'            padding: 12px 20px;\n' +
'            border: none;\n' +
'            border-radius: 8px;\n' +
'            cursor: pointer;\n' +
'            font-size: 14px;\n' +
'            font-weight: 600;\n' +
'            text-decoration: none;\n' +
'            display: inline-flex;\n' +
'            align-items: center;\n' +
'            gap: 8px;\n' +
'            transition: all 0.3s ease;\n' +
'        }\n' +
'        .back-button:hover {\n' +
'            transform: translateY(-2px);\n' +
'            box-shadow: 0 5px 15px rgba(0,0,0,0.2);\n' +
'        }\n' +
'        .toolbar {\n' +
'            padding: 25px 30px;\n' +
'            background: #f8f9fa;\n' +
'            border-bottom: 1px solid #e9ecef;\n' +
'            display: flex;\n' +
'            gap: 15px;\n' +
'            flex-wrap: wrap;\n' +
'            align-items: center;\n' +
'        }\n' +
'        .btn {\n' +
'            padding: 12px 20px;\n' +
'            border: none;\n' +
'            border-radius: 8px;\n' +
'            cursor: pointer;\n' +
'            font-size: 14px;\n' +
'            font-weight: 600;\n' +
'            transition: all 0.3s ease;\n' +
'            display: flex;\n' +
'            align-items: center;\n' +
'            gap: 8px;\n' +
'            text-decoration: none;\n' +
'            color: white;\n' +
'        }\n' +
'        .btn:hover {\n' +
'            transform: translateY(-2px);\n' +
'            box-shadow: 0 5px 15px rgba(0,0,0,0.2);\n' +
'        }\n' +
'        .btn-primary { background: linear-gradient(135deg, #28a745, #20c997); }\n' +
'        .btn-secondary { background: linear-gradient(135deg, #6c757d, #495057); }\n' +
'        .btn-danger { background: linear-gradient(135deg, #dc3545, #c82333); }\n' +
'        .btn-info { background: linear-gradient(135deg, #17a2b8, #138496); }\n' +
'        .btn-warning { background: linear-gradient(135deg, #ffc107, #e0a800); }\n' +
'        .stats {\n' +
'            padding: 20px 30px;\n' +
'            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);\n' +
'            display: flex;\n' +
'            justify-content: space-between;\n' +
'            flex-wrap: wrap;\n' +
'            gap: 20px;\n' +
'        }\n' +
'        .stat-card {\n' +
'            background: white;\n' +
'            padding: 20px;\n' +
'            border-radius: 10px;\n' +
'            box-shadow: 0 4px 6px rgba(0,0,0,0.1);\n' +
'            text-align: center;\n' +
'            flex: 1;\n' +
'            min-width: 200px;\n' +
'        }\n' +
'        .stat-number {\n' +
'            font-size: 32px;\n' +
'            font-weight: bold;\n' +
'            color: #4472C4;\n' +
'            margin-bottom: 5px;\n' +
'        }\n' +
'        .stat-label {\n' +
'            color: #6c757d;\n' +
'            font-size: 14px;\n' +
'        }\n' +
'        .content {\n' +
'            padding: 30px;\n' +
'        }\n' +
'        .table-container {\n' +
'            background: white;\n' +
'            border-radius: 10px;\n' +
'            overflow: hidden;\n' +
'            box-shadow: 0 4px 6px rgba(0,0,0,0.1);\n' +
'        }\n' +
'        table {\n' +
'            width: 100%;\n' +
'            border-collapse: collapse;\n' +
'            font-size: 12px;\n' +
'        }\n' +
'        .table-header {\n' +
'            background: #4472C4;\n' +
'            color: white;\n' +
'            font-weight: bold;\n' +
'        }\n' +
'        .table-header th {\n' +
'            padding: 15px 10px;\n' +
'            text-align: center;\n' +
'            border-right: 1px solid rgba(255,255,255,0.2);\n' +
'        }\n' +
'        tbody tr:nth-child(odd) { background: #f8f9fa; }\n' +
'        tbody tr:nth-child(even) { background: white; }\n' +
'        td {\n' +
'            padding: 12px 10px;\n' +
'            text-align: center;\n' +
'            border-right: 1px solid #e9ecef;\n' +
'            border-bottom: 1px solid #e9ecef;\n' +
'        }\n' +
'        .text-left { text-align: left !important; }\n' +
'        .text-right { text-align: right !important; }\n' +
'        .empty-state {\n' +
'            text-align: center;\n' +
'            padding: 60px 20px;\n' +
'            color: #6c757d;\n' +
'        }\n' +
'        .empty-state h3 {\n' +
'            font-size: 24px;\n' +
'            margin-bottom: 15px;\n' +
'            color: #4472C4;\n' +
'        }\n' +
'        .empty-state p {\n' +
'            font-size: 16px;\n' +
'            margin-bottom: 30px;\n' +
'        }\n' +
'        .empty-icon {\n' +
'            font-size: 64px;\n' +
'            margin-bottom: 20px;\n' +
'            opacity: 0.5;\n' +
'        }\n' +
'        @media (max-width: 768px) {\n' +
'            .container { margin: 10px; }\n' +
'            .header { padding: 20px; }\n' +
'            .toolbar { flex-direction: column; align-items: stretch; }\n' +
'            .stats { flex-direction: column; }\n' +
'            table { font-size: 10px; }\n' +
'            th, td { padding: 8px 5px; }\n' +
'        }\n' +
'    </style>\n' +
'</head>\n' +
'<body>\n' +
'    <div class="container">\n' +
'        <div class="header">\n' +
'            <div class="logo-corner">Facilite<br>Chaef</div>\n' +
'            <h1>GESTIONNAIRE DE COMMANDES - ' + groupe.nom + '</h1>\n' +
'            <p>Système de gestion des commandes clients - ' + (groupe.description || 'Groupe spécialisé') + '</p>\n' +
'        </div>\n' +
'        <div class="group-info-bar">\n' +
'            <div class="group-details">\n' +
'                <div class="group-detail">\n' +
'                    <div class="group-detail-label">Date de Création</div>\n' +
'                    <div class="group-detail-value">' + formatDate(groupe.dateCreation) + '</div>\n' +
'                </div>\n' +
'                <div class="group-detail">\n' +
'                    <div class="group-detail-label">ID Groupe</div>\n' +
'                    <div class="group-detail-value">' + groupe.id.toUpperCase() + '</div>\n' +
'                </div>\n' +
'                <div class="group-detail">\n' +
'                    <div class="group-detail-label">Statut</div>\n' +
'                    <div class="group-detail-value" style="color: #28a745;">ACTIF</div>\n' +
'                </div>\n' +
'            </div>\n' +
'            <button class="back-button" onclick="retournerGroupes()">\n' +
'                ← Retour aux Groupes\n' +
'            </button>\n' +
'        </div>\n' +
'        <div class="toolbar">\n' +
'            <button class="btn btn-primary" onclick="ouvrirModalAjout()">➕ Ajouter Commande</button>\n' +
'            <button class="btn btn-danger" onclick="supprimerCommande()">🗑️ Supprimer</button>\n' +
'            <button class="btn btn-secondary" onclick="actualiserDonnees()">🔄 Actualiser</button>\n' +
'            <button class="btn btn-warning" onclick="reinitialiserDonnees()">⚠️ Réinitialiser</button>\n' +
'            <button class="btn btn-info" onclick="exporterExcel()">📊 Exporter Excel</button>\n' +
'            <button class="btn btn-info" onclick="ouvrirFacture()">🧾 Générer Facture</button>\n' +
'        </div>\n' +
'        <div class="stats">\n' +
'            <div class="stat-card">\n' +
'                <div class="stat-number" id="totalCommandes">0</div>\n' +
'                <div class="stat-label">Total Commandes</div>\n' +
'            </div>\n' +
'            <div class="stat-card">\n' +
'                <div class="stat-number" id="totalGeneral">0 DA</div>\n' +
'                <div class="stat-label">Total Général</div>\n' +
'            </div>\n' +
'            <div class="stat-card">\n' +
'                <div class="stat-number" id="totalPaye">0 DA</div>\n' +
'                <div class="stat-label">Total Payé</div>\n' +
'            </div>\n' +
'            <div class="stat-card">\n' +
'                <div class="stat-number" id="restant">0 DA</div>\n' +
'                <div class="stat-label">Restant</div>\n' +
'            </div>\n' +
'        </div>\n' +
'        <div class="content">\n' +
'            <div class="table-container">\n' +
'                <table>\n' +
'                    <thead class="table-header">\n' +
'                        <tr id="headerRow">\n' +
'                            <th>N°</th>\n' +
'                            <th>REF CLIENT</th>\n' +
'                            <th>NOM ET PRENOM</th>\n' +
'                            <th>ARTICLES</th>\n' +
'                            <th>DATE COMMANDE</th>\n' +
'                            <th>PRIX</th>\n' +
'                            <th>TOTAL PAYÉ</th>\n' +
'                            <th>RESTANT</th>\n' +
'                        </tr>\n' +
'                    </thead>\n' +
'                    <tbody id="corpsTable">\n' +
'                        <tr>\n' +
'                            <td colspan="8">\n' +
'                                <div class="empty-state">\n' +
'                                    <div class="empty-icon">📋</div>\n' +
'                                    <h3>Aucune commande pour le moment</h3>\n' +
'                                    <p>Cliquez sur "➕ Ajouter Commande" pour commencer à ajouter des commandes à ce groupe.</p>\n' +
'                                    <button class="btn btn-primary" onclick="ouvrirModalAjout()">➕ Ajouter Première Commande</button>\n' +
'                                </div>\n' +
'                            </td>\n' +
'                        </tr>\n' +
'                    </tbody>\n' +
'                </table>\n' +
'            </div>\n' +
'        </div>\n' +
'    </div>\n\n' +
'<script>\n' +
'    const GROUPE_ID = "' + groupe.id + '";\n' +
'    const GROUPE_NOM = "' + groupe.nom + '";\n' +
'    let commandes = [];\n' +
'    let ligneSelectionnee = null;\n' +
'    let prochainId = 1;\n' +
'    const CLE_STOCKAGE = "commandesData_" + GROUPE_ID;\n\n' +
'    document.addEventListener("DOMContentLoaded", function() {\n' +
'        chargerDonnees();\n' +
'        mettreAJourStatistiques();\n' +
'    });\n\n' +
'    function chargerDonnees() {\n' +
'        const donneesStockees = localStorage.getItem(CLE_STOCKAGE);\n' +
'        if (donneesStockees) {\n' +
'            commandes = JSON.parse(donneesStockees);\n' +
'            if (commandes.length > 0) {\n' +
'                prochainId = Math.max(...commandes.map(c => c.id)) + 1;\n' +
'            }\n' +
'        }\n' +
'        afficherCommandes();\n' +
'        mettreAJourStatistiques();\n' +
'    }\n\n' +
'    function sauvegarderDonnees() {\n' +
'        localStorage.setItem(CLE_STOCKAGE, JSON.stringify(commandes));\n' +
'        mettreAJourStatistiquesGroupe();\n' +
'    }\n\n' +
'    function afficherCommandes() {\n' +
'        const tbody = document.getElementById("corpsTable");\n' +
'        tbody.innerHTML = "";\n' +
'        \n' +
'        if (commandes.length === 0) {\n' +
'            tbody.innerHTML = \'<tr><td colspan="8"><div class="empty-state"><div class="empty-icon">📋</div><h3>Aucune commande pour le moment</h3><p>Cliquez sur "➕ Ajouter Commande" pour commencer à ajouter des commandes à ce groupe.</p><button class="btn btn-primary" onclick="ouvrirModalAjout()">➕ Ajouter Première Commande</button></div></td></tr>\';\n' +
'            return;\n' +
'        }\n\n' +
'        commandes.forEach(commande => {\n' +
'            const tr = document.createElement("tr");\n' +
'            tr.onclick = () => selectionnerLigne(tr, commande.id);\n' +
'            const restant = commande.prix - commande.totalPaye;\n' +
'            \n' +
'            tr.innerHTML = "<td>" + commande.id + "</td>" +\n' +
'                          "<td class=\\"text-left\\">" + commande.refClient + "</td>" +\n' +
'                          "<td class=\\"text-left\\">" + commande.nomPrenom + "</td>" +\n' +
'                          "<td class=\\"text-left\\">" + commande.articles + "</td>" +\n' +
'                          "<td>" + commande.dateCommande + "</td>" +\n' +
'                          "<td class=\\"text-right\\">" + commande.prix.toFixed(2) + "</td>" +\n' +
'                          "<td class=\\"text-right\\">" + commande.totalPaye.toFixed(2) + "</td>" +\n' +
'                          "<td class=\\"text-right\\">" + restant.toFixed(2) + "</td>";\n' +
'            tbody.appendChild(tr);\n' +
'        });\n\n' +
'        if (commandes.length > 0) {\n' +
'            const totalRow = document.createElement("tr");\n' +
'            totalRow.style.backgroundColor = "#e6f2ff";\n' +
'            totalRow.style.fontWeight = "bold";\n' +
'            totalRow.style.borderTop = "2px solid #4472C4";\n' +
'            \n' +
'            const totalPrix = commandes.reduce((sum, cmd) => sum + cmd.prix, 0);\n' +
'            const totalPaye = commandes.reduce((sum, cmd) => sum + cmd.totalPaye, 0);\n' +
'            const totalRestant = totalPrix - totalPaye;\n' +
'            \n' +
'            totalRow.innerHTML = "<td colspan=\\"5\\"><strong>TOTAL</strong></td>" +\n' +
'                                "<td class=\\"text-right\\"><strong>" + totalPrix.toFixed(2) + "</strong></td>" +\n' +
'                                "<td class=\\"text-right\\"><strong>" + totalPaye.toFixed(2) + "</strong></td>" +\n' +
'                                "<td class=\\"text-right\\"><strong>" + totalRestant.toFixed(2) + "</strong></td>";\n' +
'            tbody.appendChild(totalRow);\n' +
'        }\n' +
'    }\n\n' +
'    function mettreAJourStatistiques() {\n' +
'        const totalCommandes = commandes.length;\n' +
'        const totalGeneral = commandes.reduce((sum, cmd) => sum + cmd.prix, 0);\n' +
'        const totalPaye = commandes.reduce((sum, cmd) => sum + cmd.totalPaye, 0);\n' +
'        const restant = totalGeneral - totalPaye;\n\n' +
'        document.getElementById("totalCommandes").textContent = totalCommandes;\n' +
'        document.getElementById("totalGeneral").textContent = new Intl.NumberFormat("fr-FR").format(totalGeneral) + " DA";\n' +
'        document.getElementById("totalPaye").textContent = new Intl.NumberFormat("fr-FR").format(totalPaye) + " DA";\n' +
'        document.getElementById("restant").textContent = new Intl.NumberFormat("fr-FR").format(restant) + " DA";\n' +
'    }\n\n' +
'    function mettreAJourStatistiquesGroupe() {\n' +
'        const groupes = JSON.parse(localStorage.getItem("groupes") || "[]");\n' +
'        const groupeIndex = groupes.findIndex(g => g.id === GROUPE_ID);\n' +
'        if (groupeIndex !== -1) {\n' +
'            groupes[groupeIndex].nombreClients = commandes.length;\n' +
'            groupes[groupeIndex].chiffreAffaires = commandes.reduce((total, cmd) => total + (cmd.prix || 0), 0);\n' +
'            localStorage.setItem("groupes", JSON.stringify(groupes));\n' +
'        }\n' +
'    }\n\n' +
'    function selectionnerLigne(tr, id) {\n' +
'        if (ligneSelectionnee) {\n' +
'            ligneSelectionnee.style.backgroundColor = "";\n' +
'        }\n' +
'        tr.style.backgroundColor = "#e3f2fd";\n' +
'        ligneSelectionnee = tr;\n' +
'        window.commandeSelectionnee = id;\n' +
'    }\n\n' +
'    function ouvrirModalAjout() {\n' +
'        const refClient = prompt("Référence Client:");\n' +
'        if (!refClient) return;\n' +
'        \n' +
'        const nomPrenom = prompt("Nom et Prénom:");\n' +
'        if (!nomPrenom) return;\n' +
'        \n' +
'        const articles = prompt("Articles:");\n' +
'        if (!articles) return;\n' +
'        \n' +
'        const prix = parseFloat(prompt("Prix:"));\n' +
'        if (isNaN(prix)) return;\n' +
'        \n' +
'        const totalPaye = parseFloat(prompt("Total Payé:")) || 0;\n\n' +
'        const nouvelleCommande = {\n' +
'            id: prochainId++,\n' +
'            refClient: refClient,\n' +
'            nomPrenom: nomPrenom,\n' +
'            articles: articles,\n' +
'            dateCommande: new Date().toLocaleDateString("fr-FR"),\n' +
'            prix: prix,\n' +
'            totalPaye: totalPaye\n' +
'        };\n\n' +
'        commandes.push(nouvelleCommande);\n' +
'        sauvegarderDonnees();\n' +
'        chargerDonnees();\n' +
'        \n' +
'        alert("✅ Commande ajoutée avec succès!");\n' +
'    }\n\n' +
'    function supprimerCommande() {\n' +
'        if (window.commandeSelectionnee) {\n' +
'            if (confirm("Supprimer la commande sélectionnée ?")) {\n' +
'                commandes = commandes.filter(c => c.id !== window.commandeSelectionnee);\n' +
'                sauvegarderDonnees();\n' +
'                chargerDonnees();\n' +
'                window.commandeSelectionnee = null;\n' +
'                ligneSelectionnee = null;\n' +
'                alert("✅ Commande supprimée!");\n' +
'            }\n' +
'        } else {\n' +
'            alert("⚠️ Veuillez sélectionner une commande à supprimer");\n' +
'        }\n' +
'    }\n\n' +
'    function actualiserDonnees() {\n' +
'        chargerDonnees();\n' +
'        alert("🔄 Données actualisées pour " + GROUPE_NOM);\n' +
'    }\n\n' +
'    function reinitialiserDonnees() {\n' +
'        if (confirm("⚠️ Réinitialiser toutes les données de " + GROUPE_NOM + " ?\\n\\nCette action supprimera toutes les commandes et ne peut pas être annulée!")) {\n' +
'            localStorage.removeItem(CLE_STOCKAGE);\n' +
'            commandes = [];\n' +
'            prochainId = 1;\n' +
'            chargerDonnees();\n' +
'            alert("✅ Données réinitialisées pour " + GROUPE_NOM);\n' +
'        }\n' +
'    }\n\n' +
'    function exporterExcel() {\n' +
'        if (commandes.length === 0) {\n' +
'            alert("⚠️ Aucune donnée à exporter!");\n' +
'            return;\n' +
'        }\n' +
'        alert("📊 Export Excel pour " + GROUPE_NOM + " (fonctionnalité à développer)");\n' +
'    }\n\n' +
'    function ouvrirFacture() {\n' +
'        if (commandes.length === 0) {\n' +
'            alert("⚠️ Aucune commande pour générer une facture!");\n' +
'            return;\n' +
'        }\n' +
'        alert("🧾 Génération de facture pour " + GROUPE_NOM + " (fonctionnalité à développer)");\n' +
'    }\n\n' +
'    function retournerGroupes() {\n' +
'        if (confirm("Retourner à la liste des groupes?\\n\\nVos données sont automatiquement sauvegardées.")) {\n' +
'            window.close();\n' +
'        }\n' +
'    }\n' +
'</script>\n' +
'</body>\n' +
'</html>';
        }

        function formatDate(dateStr) {
            const date = new Date(dateStr);
            return date.toLocaleDateString('fr-FR');
        }

        function formatMontant(montant) {
            return new Intl.NumberFormat('fr-FR').format(montant);
        }

        window.onclick = function(event) {
            const modal = document.getElementById('modalNouveauGroupe');
            if (event.target === modal) {
                fermerModal();
            }
        }

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                fermerModal();
            }
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                ouvrirModalNouveauGroupe();
            }
        });
    </script>
</body>
</html>
