<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facture - DJEMIAL MOUSTAPHA</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.2;
            background: white;
        }
        
        .facture-container {
            width: 100%;
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            border: 2px solid #000;
        }
        
        /* En-tête avec informations de l'entreprise */
        .header-section {
            display: flex;
            border-bottom: 2px solid #000;
        }
        
        .company-info {
            flex: 1;
            padding: 10px;
            border-right: 2px solid #000;
        }
        
        .company-info h3 {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .logo-section {
            width: 80px;
            padding: 10px;
            text-align: center;
            border-right: 2px solid #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd700);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 8px;
            text-align: center;
            border: 3px solid #333;
            overflow: hidden;
            position: relative;
            cursor: pointer;
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .logo-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
        }

        .logo-upload {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            z-index: 2;
        }
        
        .facture-title {
            width: 120px;
            padding: 10px;
            text-align: center;
            border-right: 2px solid #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .facture-title h2 {
            background: #ffd700;
            padding: 8px 15px;
            border: 2px solid #000;
            font-size: 14px;
            font-weight: bold;
        }
        
        .registration-info {
            flex: 1;
            padding: 10px;
            font-size: 9px;
        }
        
        .registration-info div {
            margin-bottom: 3px;
        }
        
        /* Section date et numéro de facture */
        .date-section {
            display: flex;
            background: #4472C4;
            color: white;
            font-weight: bold;
        }
        
        .date-left {
            flex: 1;
            padding: 8px;
            text-align: center;
        }
        
        .date-right {
            flex: 1;
            padding: 8px;
            text-align: center;
            border-left: 1px solid white;
        }
        
        /* Tableau des articles */
        .table-container {
            width: 100%;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
        }
        
        .table-header {
            background: #4472C4;
            color: white;
            font-weight: bold;
        }
        
        .table-header th {
            padding: 8px 4px;
            text-align: center;
            border: 1px solid white;
            font-size: 9px;
        }
        
        tbody tr:nth-child(odd) {
            background: #f8f9fa;
        }
        
        tbody tr:nth-child(even) {
            background: white;
        }
        
        td {
            padding: 6px 4px;
            text-align: center;
            border: 1px solid #ccc;
            font-size: 9px;
        }
        
        .text-left {
            text-align: left !important;
        }
        
        .text-right {
            text-align: right !important;
        }
        
        /* Ligne total */
        .total-row {
            background: #e6f2ff !important;
            font-weight: bold;
        }
        
        /* Section signature */
        .signature-section {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #000;
            text-align: center;
            font-size: 10px;
            background: #f8f9fa;
        }
        
        /* Grille pour signature */
        .grid-section {
            margin-top: 10px;
            height: 200px;
            background-image: 
                linear-gradient(to right, #ddd 1px, transparent 1px),
                linear-gradient(to bottom, #ddd 1px, transparent 1px);
            background-size: 20px 20px;
            border: 1px solid #ccc;
        }
        
        @media print {
            body {
                margin: 0;
                padding: 0;
            }
            
            .facture-container {
                border: none;
                box-shadow: none;
            }
            
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="facture-container">
        <!-- En-tête -->
        <div class="header-section">
            <div class="company-info">
                <h3>DJEMIAL MOUSTAPHA</h3>
                <div>CITE 500 LOGEMENT - LAGHOUAT</div>
                <div>TEL : 07-77-98-38</div>
                <div>E-mail : <EMAIL></div>
            </div>
            
            <div class="logo-section">
                <div class="logo" onclick="document.getElementById('logoUpload').click()" title="Cliquer pour changer le logo">
                    <input type="file" id="logoUpload" class="logo-upload" accept="image/*" onchange="changerLogo(this)">
                    <img id="logoImage" style="display: none;">
                    <div class="logo-text" id="logoText">
                        Facilite<br>Chaef
                    </div>
                </div>
            </div>
            
            <div class="facture-title">
                <h2>FACTURE</h2>
            </div>
            
            <div class="registration-info">
                <div><strong>N° REGISTRE DE COMMERCE :</strong> 03/00-4639233A21</div>
                <div><strong>N° COMPTE COMMERCIAL :</strong> BANQUE AGB 288614/208/09</div>
                <div><strong>N° NIS :</strong> 199030010184437</div>
                <div><strong>N° NIF :</strong> 230103222210</div>
            </div>
        </div>
        
        <!-- Section date -->
        <div class="date-section">
            <div class="date-left">DATE: <span id="currentDate">21/10/2024</span></div>
            <div class="date-right">FACTURE GROUPE N° 02 | Versement Echelonné N° <span id="versementNum">1</span> /10</div>
        </div>
        
        <!-- Tableau -->
        <div class="table-container">
            <table>
                <thead class="table-header">
                    <tr>
                        <th style="width: 30px;">N°</th>
                        <th style="width: 80px;">REF CLIENT</th>
                        <th style="width: 150px;">NOM ET PRENOM</th>
                        <th style="width: 200px;">ARTICLES</th>
                        <th style="width: 80px;">DATE D'ACHAT</th>
                        <th style="width: 70px;">PRIX</th>
                        <th style="width: 80px;">TOTAL PAYE</th>
                        <th style="width: 80px;">ECHEANCE N°</th>
                        <th style="width: 30px;">1</th>
                    </tr>
                </thead>
                <tbody id="factureTableBody">
                    <!-- Les données seront ajoutées ici par JavaScript -->
                </tbody>
            </table>
        </div>
        
        <!-- Section signature -->
        <div class="signature-section">
            <strong>توقيعات هذه الفاتورة غير مبلغ قدره : ستة وستون الف وثلاثة مئة وخمسة وسبعون دينار جزائري</strong>
        </div>
        
        <!-- Grille pour signatures -->
        <div class="grid-section"></div>
    </div>

    <!-- Boutons de contrôle (cachés lors de l'impression) -->
    <div class="no-print" style="text-align: center; margin: 20px;">
        <button onclick="window.print()" style="background: #4472C4; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">🖨️ Imprimer</button>
        <button onclick="remplirDonneesExemple()" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">📊 Remplir Exemple</button>
        <button onclick="viderTableau()" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">🗑️ Vider</button>
        <button onclick="document.getElementById('logoUpload').click()" style="background: #ff6b35; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">🖼️ Changer Logo</button>
        <button onclick="restaurerLogoDefaut()" style="background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">↩️ Logo Défaut</button>
        <button onclick="testerConversionNombre()" style="background: #17a2b8; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">🔢 Test Conversion</button>
    </div>

    <script>
        // Mettre à jour la date actuelle
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('fr-FR');
        
        // Fonction pour remplir avec des données d'exemple
        function remplirDonneesExemple() {
            const donneesExemple = [
                {num: 1, ref: "CS2451793", nom: "BEN SEGHIR ABDERAHMANE", articles: "TELE REALME C55 8/256", date: "24/09/2024", prix: "48000.00", totalPaye: "28800.00", echeance: "4800.00", statut: "1"},
                {num: 2, ref: "CS2451796", nom: "SIGA MOHAMMED", articles: "REF 580L+TV 40'' VDR+TENDEUSE", date: "25/09/2024", prix: "142500.00", totalPaye: "85740.00", echeance: "14250.00", statut: ""},
                {num: 3, ref: "CS2451805", nom: "CHAABNA SAID", articles: "MEUBLES", date: "05/10/2024", prix: "22800.00", totalPaye: "13680.00", echeance: "2280.00", statut: ""},
                {num: 4, ref: "CS2451807", nom: "BEKKAYE ABDELBAKI", articles: "RADIATEUR A GAZ FG116", date: "06/10/2024", prix: "33000.00", totalPaye: "19800.00", echeance: "3300.00", statut: ""},
                {num: 5, ref: "CS2451814", nom: "MAZHOUB KHAIRA", articles: "M LAVER 10.5KG RAYLAN +TV 43'' VEDOS", date: "07/10/2024", prix: "158000.00", totalPaye: "94800.00", echeance: "15800.00", statut: ""},
                {num: 6, ref: "CS2451810", nom: "KOUIDER FATEH", articles: "CUISINIER 4 FEUX ENEIM", date: "07/10/2024", prix: "69000.00", totalPaye: "20700.00", echeance: "6900.00", statut: ""},
                {num: 7, ref: "CS2451811", nom: "DJAMED ELHOUCINE", articles: "TV 43'' GOOGLE TV STREAM", date: "07/10/2024", prix: "61000.00", totalPaye: "36600.00", echeance: "6100.00", statut: ""},
                {num: 8, ref: "CS2451822", nom: "DJIREB AMINA", articles: "CHAUF-EAU ELF+RADIATEUR GAZ", date: "09/10/2024", prix: "49750.00", totalPaye: "29850.00", echeance: "4975.00", statut: ""},
                {num: 9, ref: "CS2451830", nom: "HAMDANI HICHAM", articles: "RADIATEUR GAZ 14KW +TENDEUSE", date: "12/10/2024", prix: "55300.00", totalPaye: "33180.00", echeance: "5530.00", statut: ""},
                {num: 10, ref: "CS2451832", nom: "DZIRI MOHAMED", articles: "MICRO ONDES 23 L", date: "14/10/2024", prix: "24000.00", totalPaye: "2400.00", echeance: "2400.00", statut: ""}
            ];
            
            remplirTableau(donneesExemple);
        }
        
        // Fonction pour remplir le tableau
        function remplirTableau(donnees) {
            const tbody = document.getElementById('factureTableBody');
            tbody.innerHTML = '';
            
            let totalPrix = 0;
            let totalPaye = 0;
            
            donnees.forEach(item => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${item.num}</td>
                    <td class="text-left">${item.ref}</td>
                    <td class="text-left">${item.nom}</td>
                    <td class="text-left">${item.articles}</td>
                    <td>${item.date}</td>
                    <td class="text-right">${item.prix}</td>
                    <td class="text-right">${item.totalPaye}</td>
                    <td class="text-right">${item.echeance}</td>
                    <td>${item.statut}</td>
                `;
                
                totalPrix += parseFloat(item.prix);
                totalPaye += parseFloat(item.totalPaye);
            });
            
            // Ligne total
            const totalRow = tbody.insertRow();
            totalRow.className = 'total-row';
            totalRow.innerHTML = `
                <td colspan="5"><strong>TOTAL</strong></td>
                <td class="text-right"><strong>${totalPrix.toFixed(2)}</strong></td>
                <td class="text-right"><strong>${totalPaye.toFixed(2)}</strong></td>
                <td class="text-right"><strong>${(totalPrix - totalPaye).toFixed(2)}</strong></td>
                <td></td>
            `;

            // Mettre à jour automatiquement le texte en lettres
            setTimeout(() => {
                mettreAJourTotalEnLettres();
            }, 100);
        }
        
        // Fonction pour vider le tableau
        function viderTableau() {
            document.getElementById('factureTableBody').innerHTML = '';
        }
        
        // Fonction pour charger les données depuis localStorage (du gestionnaire principal)
        function chargerDonneesGestionnaire() {
            const commandesData = localStorage.getItem('commandesData');
            if (commandesData) {
                const commandes = JSON.parse(commandesData);
                const donneesFacture = commandes.map((cmd, index) => ({
                    num: index + 1,
                    ref: cmd.refClient,
                    nom: cmd.nomPrenom,
                    articles: cmd.articles,
                    date: cmd.dateCommande,
                    prix: cmd.prix.toFixed(2),
                    totalPaye: cmd.totalPaye.toFixed(2),
                    echeance: (cmd.prix / 10).toFixed(2), // Échéance = prix / 10
                    statut: cmd.statuts && cmd.statuts[0] ? "1" : ""
                }));
                remplirTableau(donneesFacture);
            } else {
                // Si pas de données, charger l'exemple
                remplirDonneesExemple();
            }
        }

        // Fonction pour convertir nombre en lettres (français)
        function nombreEnLettres(nombre) {
            if (nombre === 0) return 'zéro';

            const unites = ['', 'un', 'deux', 'trois', 'quatre', 'cinq', 'six', 'sept', 'huit', 'neuf',
                           'dix', 'onze', 'douze', 'treize', 'quatorze', 'quinze', 'seize', 'dix-sept', 'dix-huit', 'dix-neuf'];

            const dizaines = ['', '', 'vingt', 'trente', 'quarante', 'cinquante', 'soixante', 'soixante-dix', 'quatre-vingt', 'quatre-vingt-dix'];

            function convertirGroupe(n) {
                let resultat = '';

                // Centaines
                const centaines = Math.floor(n / 100);
                if (centaines > 0) {
                    if (centaines === 1) {
                        resultat += 'cent';
                    } else {
                        resultat += unites[centaines] + ' cent';
                        if (n % 100 === 0) resultat += 's'; // cents
                    }
                }

                // Dizaines et unités
                const reste = n % 100;
                if (reste > 0) {
                    if (resultat) resultat += ' ';

                    if (reste < 20) {
                        resultat += unites[reste];
                    } else {
                        const diz = Math.floor(reste / 10);
                        const unit = reste % 10;

                        if (diz === 7 || diz === 9) {
                            // Cas spéciaux: 70-79 et 90-99
                            if (diz === 7) {
                                resultat += 'soixante';
                                if (unit === 1) resultat += ' et onze';
                                else if (unit > 1) resultat += '-' + unites[10 + unit];
                                else resultat += '-dix';
                            } else { // diz === 9
                                resultat += 'quatre-vingt';
                                if (unit === 1) resultat += ' et onze';
                                else if (unit > 1) resultat += '-' + unites[10 + unit];
                                else resultat += '-dix';
                            }
                        } else if (diz === 8) {
                            resultat += 'quatre-vingt';
                            if (unit === 0) resultat += 's';
                            else resultat += '-' + unites[unit];
                        } else {
                            resultat += dizaines[diz];
                            if (unit === 1 && diz !== 8) resultat += ' et un';
                            else if (unit > 1) resultat += '-' + unites[unit];
                        }
                    }
                }

                return resultat;
            }

            // Traitement des millions
            if (nombre >= 1000000) {
                const millions = Math.floor(nombre / 1000000);
                const reste = nombre % 1000000;
                let resultat = '';

                if (millions === 1) {
                    resultat = 'un million';
                } else {
                    resultat = convertirGroupe(millions) + ' millions';
                }

                if (reste > 0) {
                    resultat += ' ' + nombreEnLettres(reste);
                }

                return resultat;
            }

            // Traitement des milliers
            if (nombre >= 1000) {
                const milliers = Math.floor(nombre / 1000);
                const reste = nombre % 1000;
                let resultat = '';

                if (milliers === 1) {
                    resultat = 'mille';
                } else {
                    resultat = convertirGroupe(milliers) + ' mille';
                }

                if (reste > 0) {
                    resultat += ' ' + convertirGroupe(reste);
                }

                return resultat;
            }

            return convertirGroupe(nombre);
        }

        // Mettre à jour le texte en lettres du total
        function mettreAJourTotalEnLettres() {
            const rows = document.querySelectorAll('#factureTableBody tr:not(.total-row)');
            let total = 0;
            rows.forEach(row => {
                const prixCell = row.cells[5];
                if (prixCell) {
                    total += parseFloat(prixCell.textContent.replace(/[^\d.-]/g, ''));
                }
            });

            const totalEnLettres = nombreEnLettres(Math.floor(total));
            const signatureSection = document.querySelector('.signature-section strong');

            // Texte en français avec le montant en lettres
            signatureSection.innerHTML = `
                <div style="margin-bottom: 5px;">
                    <strong>Arrêtée la présente facture à la somme de : ${totalEnLettres} dinars algériens</strong>
                </div>
                <div style="font-size: 10px; color: #666;">
                    توقيعات هذه الفاتورة غير مبلغ قدره : ${totalEnLettres} دينار جزائري
                </div>
            `;
        }

        // Fonction pour changer le logo
        function changerLogo(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const logoImage = document.getElementById('logoImage');
                    const logoText = document.getElementById('logoText');

                    logoImage.src = e.target.result;
                    logoImage.style.display = 'block';
                    logoText.style.display = 'none';

                    // Sauvegarder l'image dans localStorage
                    localStorage.setItem('logoFacture', e.target.result);
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Fonction pour restaurer le logo par défaut
        function restaurerLogoDefaut() {
            const logoImage = document.getElementById('logoImage');
            const logoText = document.getElementById('logoText');

            logoImage.style.display = 'none';
            logoText.style.display = 'block';

            // Supprimer de localStorage
            localStorage.removeItem('logoFacture');
        }

        // Fonction pour charger le logo sauvegardé
        function chargerLogoSauvegarde() {
            const logoSauvegarde = localStorage.getItem('logoFacture');
            if (logoSauvegarde) {
                const logoImage = document.getElementById('logoImage');
                const logoText = document.getElementById('logoText');

                logoImage.src = logoSauvegarde;
                logoImage.style.display = 'block';
                logoText.style.display = 'none';
            }
        }

        // Fonction pour tester la conversion de nombres
        function testerConversionNombre() {
            const nombre = prompt("Entrez un nombre à convertir en lettres (exemple: 66375):");
            if (nombre && !isNaN(nombre)) {
                const nombreInt = parseInt(nombre);
                const resultat = nombreEnLettres(nombreInt);
                alert(`${nombreInt} = ${resultat} dinars algériens`);
            }
        }

        // Charger les données au démarrage
        chargerDonneesGestionnaire();
        chargerLogoSauvegarde();
    </script>
</body>
</html>
